package com.engine.grtool.doc.util;

import com.aspose.words.*;
import java.awt.Color;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * @FileName WordsUtil.java
 * @Description words工具类，处理word文档、pdf文档这些
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/26
 */
public class WordsUtil {
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(WordsUtil.class);


    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user) {
        return doc2Pdf(orginDocId, user, "");
    }

    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user, String waterStr) {
        DocFileInfo fileInfo = DocUtil.getFirstFileWithInputStream(orginDocId);
        String fileName = Util.null2String(fileInfo.getFileName());
        String fileNameWithOutExt = fileName;
        if (fileName.contains(".")) {
            fileNameWithOutExt = fileName.substring(0, fileName.lastIndexOf("."));
        }
        String newFileName = fileNameWithOutExt + ".pdf";
        InputStream orginDocInputStream = fileInfo.getFileInputStream();
        if (orginDocInputStream != null) {
            return doc2Pdf(orginDocInputStream, newFileName, user, fileInfo.getSeccategory(), waterStr);
        }
        return -1;
    }

    /**
     *
     * @param orginDocInputStream
     * @param pdfFileName
     * @param user
     * @param newDocCategoryId
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(InputStream orginDocInputStream, String pdfFileName, User user, int newDocCategoryId, String waterStr) {
        int newPdfDocId = -1;
        InputStream newFileIs;
        String tempFilePath = "";
        Document docss;
        FileOutputStream osll = null;
        try {
            //创建临时文件目录
            String tempPdfFileFolder = GCONST.getRootPath() + File.separator + "grsd" + File.separator + "temp";
            createTempFileFolder(tempPdfFileFolder);
            //创建历史文件
            String uuid = UUID.randomUUID().toString();
            tempFilePath = tempPdfFileFolder + File.separator + uuid + ".pdf";
            //创建文件输出流
            osll = new FileOutputStream(tempFilePath);
            //load授权
            loadLicense();
            try {
                docss = new Document(orginDocInputStream);

                // 如果需要添加水印，在Word文档中添加水印
                if (!Util.null2String(waterStr).isEmpty()) {
                    //插入水印到Word文档
                    insertWatermark(docss, waterStr);

                    // 保存加了水印的Word文档，方便排查
                    String tempWordFilePath = tempFilePath.replace(".pdf", "_watermark.docx");
                    docss.save(tempWordFilePath, SaveFormat.DOCX);
                    log.info("已保存带水印的Word文档用于排查: " + tempWordFilePath);
                }

                // 转换为PDF
                docss.save(osll, SaveFormat.PDF);

                //获取临时文件的输入流
                newFileIs = Files.newInputStream(Paths.get(tempFilePath));
                //根据临时文件输出流，创建OA文档
                newPdfDocId = DocUtil.createDocWithFile(newFileIs, user, pdfFileName, newDocCategoryId);
            } catch (Exception e) {
                log.error("doc2Pdf error", e);
            }
        } catch (Exception e1) {
            log.error("doc2Pdf异常,", e1);
        } finally {
            //关闭文件原始文件输入流
            try {
                orginDocInputStream.close();
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //关闭目标文件输出流
            try {
                if (osll != null) {
                    osll.close();
                }
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //删除临时文件
//            if (osll != null) {
//                File file = new File(tempFilePath);
//                if (file.exists()) {
//                    file.delete();
//                }
//            }
        }
        return newPdfDocId;
    }

    /**
     * 创建临时目录
     *
     * @param tempPdfFilePath
     */
    private static void createTempFileFolder(String tempPdfFilePath) {
        //判断该目录是否存在
        File tempPdfFileDir = new File(tempPdfFilePath);
        if (!tempPdfFileDir.exists()) {
            tempPdfFileDir.mkdirs();
        }
    }

    /**
     * 加载授权
     */
    private static void loadLicense() {
        // 返回读取指定资源的输入流
        License license = new License();
        InputStream is = null;
        try {
            //String licenseFile = "/Users/<USER>/Documents/WEAVER/otherlib/aspose-words-14.9.0-jdk16-license.xml";
            String licenseFile = GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "lib" + File.separator + "aspose-words-14.9.0-jdk16-license.xml";
            //获取文件流
            is = Files.newInputStream(Paths.get(licenseFile));
            license.setLicense(is);
        } catch (Exception e) {
            log.error("loadLicense异常,", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("loadLicense释放资源异常", e);
                }
            }
        }
    }

    /**
     * 插入水印到Word文档
     * 根据截图要求，实现斜向重复水印效果，确保水印覆盖整个页面包括图片和表格
     *
     * @param doc           Word文档对象
     * @param watermarkText 水印文本
     * @throws Exception 异常
     */
    public static void insertWatermark(Document doc, String watermarkText) throws Exception {
        log.info("开始插入水印，水印文本: " + watermarkText);

        // 创建多个水印形状以覆盖整个页面
        for (Section sect : doc.getSections()) {
            // 为每个节添加水印到页眉中，确保在所有内容之上
            insertWatermarkIntoHeader(doc, sect, HeaderFooterType.HEADER_PRIMARY, watermarkText);
            insertWatermarkIntoHeader(doc, sect, HeaderFooterType.HEADER_FIRST, watermarkText);
            insertWatermarkIntoHeader(doc, sect, HeaderFooterType.HEADER_EVEN, watermarkText);
        }

        log.info("水印插入完成");
    }

    /**
     * 在页眉中插入水印
     *
     * @param doc           文档对象
     * @param sect          节对象
     * @param headerType    页眉类型
     * @param watermarkText 水印文本
     * @throws Exception 异常
     */
    private static void insertWatermarkIntoHeader(Document doc, Section sect, int headerType, String watermarkText) throws Exception {
        HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(headerType);
        if (header == null) {
            header = new HeaderFooter(doc, headerType);
            sect.getHeadersFooters().add(header);
        }

        // 清除现有内容，避免重复添加
        header.removeAllChildren();

        // 创建多个水印以覆盖整个页面
        // 根据截图，需要创建网格状的水印分布
        createWatermarkGrid(doc, header, watermarkText);
    }

    /**
     * 创建网格状水印分布
     *
     * @param doc           文档对象
     * @param header        页眉对象
     * @param watermarkText 水印文本
     * @throws Exception 异常
     */
    private static void createWatermarkGrid(Document doc, HeaderFooter header, String watermarkText) throws Exception {
        // 创建一个段落来容纳所有水印
        Paragraph watermarkPara = new Paragraph(doc);

        // 根据截图效果，创建多个水印形状形成网格
        // 水印间距和位置参数
        int rows = 8;    // 行数
        int cols = 6;    // 列数
        double pageWidth = 595;   // A4页面宽度（点）
        double pageHeight = 842;  // A4页面高度（点）
        double watermarkWidth = 200;  // 水印宽度
        double watermarkHeight = 60;  // 水印高度

        // 计算水印间距
        double horizontalSpacing = pageWidth / cols;
        double verticalSpacing = pageHeight / rows;

        for (int row = 0; row < rows; row++) {
            for (int col = 0; col < cols; col++) {
                // 创建水印形状
                Shape watermark = createWatermarkShape(doc, watermarkText, watermarkWidth, watermarkHeight);

                // 设置水印位置
                double left = col * horizontalSpacing - watermarkWidth / 2;
                double top = row * verticalSpacing - watermarkHeight / 2;

                // 设置绝对定位
                watermark.setLeft(left);
                watermark.setTop(top);
                watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
                watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);
                watermark.setWrapType(WrapType.NONE);

                // 添加到段落
                watermarkPara.appendChild(watermark);
            }
        }

        // 将段落添加到页眉
        header.appendChild(watermarkPara);
    }

    /**
     * 创建单个水印形状
     *
     * @param doc             文档对象
     * @param watermarkText   水印文本
     * @param width          水印宽度
     * @param height         水印高度
     * @return 水印形状对象
     * @throws Exception 异常
     */
    private static Shape createWatermarkShape(Document doc, String watermarkText, double width, double height) throws Exception {
        // 创建文本水印形状
        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);

        // 设置水印文本
        watermark.getTextPath().setText(watermarkText);
        watermark.getTextPath().setFontFamily("宋体");
        watermark.getTextPath().setBold(false);

        // 设置水印尺寸
        watermark.setWidth(width);
        watermark.setHeight(height);

        // 设置旋转角度（斜向效果）
        watermark.setRotation(-45);

        // 设置水印颜色和透明度
        watermark.getFill().setColor(new Color(192, 192, 192, 100)); // 浅灰色，半透明
        watermark.setStrokeColor(new Color(192, 192, 192, 100));
        watermark.getFill().setTransparency(0.6); // 设置透明度

        // 设置水印在最底层，不影响文档内容
        watermark.setBehindText(true);
        watermark.setZOrder(-1);

        return watermark;
    }


}